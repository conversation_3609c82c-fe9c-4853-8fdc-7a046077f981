#!/usr/bin/env python3
"""
Test script to validate the multi-agent system improvements.
This script tests the key fixes implemented for coordination deadlocks and API communication issues.
"""

import asyncio
import json
import time
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env2_box_arrange_async import run_exp_async, detect_deadlock, calculate_progress_score, generate_strategic_moves
from env2_create import action_from_response, judge_move_box2pos_box2target_func
from LLM_async import get_available_async_client, mark_async_endpoint_unavailable, mark_async_endpoint_available
from LLM import get_available_client, mark_endpoint_unavailable, mark_endpoint_available

def test_collision_detection():
    """Test the improved collision detection logic"""
    print("=" * 60)
    print("TESTING: Enhanced Collision Detection")
    print("=" * 60)
    
    # Create a test scenario with potential collisions
    test_pg_dict = {
        '0.0_0.0': ['workpiece_blue'],
        '0.0_1.0': [],
        '1.0_0.0': ['workpiece_red'],
        '1.0_1.0': [],
        '0.5_0.5': [],
        '1.5_0.5': []
    }
    
    # Test case 1: Valid non-conflicting actions
    print("\nTest 1: Valid non-conflicting actions")
    valid_response = {
        'Agent[0.5, 0.5]': 'move(workpiece_blue, position[0.0, 1.0])',
        'Agent[1.5, 0.5]': 'move(workpiece_red, position[1.0, 1.0])'
    }
    
    system_error, updated_dict, collision = action_from_response(test_pg_dict, valid_response)
    print(f"System Error: {system_error}")
    print(f"Collision Detected: {collision}")
    print(f"Result: {'PASS' if not collision and not system_error else 'FAIL'}")
    
    # Test case 2: Collision scenario - both agents target same position
    print("\nTest 2: Collision scenario - same target position")
    collision_response = {
        'Agent[0.5, 0.5]': 'move(workpiece_blue, position[0.0, 1.0])',
        'Agent[1.5, 0.5]': 'move(workpiece_red, position[0.0, 1.0])'  # Same target!
    }
    
    system_error, updated_dict, collision = action_from_response(test_pg_dict, collision_response)
    print(f"System Error: {system_error}")
    print(f"Collision Detected: {collision}")
    print(f"Result: {'PASS' if collision else 'FAIL'}")
    
    # Test case 3: Invalid workpiece reference
    print("\nTest 3: Invalid workpiece reference")
    invalid_response = {
        'Agent[0.5, 0.5]': 'move(workpiece_nonexistent, position[0.0, 1.0])'
    }
    
    system_error, updated_dict, collision = action_from_response(test_pg_dict, invalid_response)
    print(f"System Error: {system_error}")
    print(f"Collision Detected: {collision}")
    print(f"Result: {'PASS' if system_error and not collision else 'FAIL'}")

def test_deadlock_detection():
    """Test the enhanced deadlock detection mechanisms"""
    print("\n" + "=" * 60)
    print("TESTING: Enhanced Deadlock Detection")
    print("=" * 60)
    
    # Create test state history that shows a cycle
    test_states = [
        {'0.0_0.0': ['workpiece_blue'], '1.0_0.0': ['workpiece_red']},
        {'0.0_1.0': ['workpiece_blue'], '1.0_0.0': ['workpiece_red']},
        {'0.0_0.0': ['workpiece_blue'], '1.0_0.0': ['workpiece_red']},  # Back to state 1
        {'0.0_1.0': ['workpiece_blue'], '1.0_0.0': ['workpiece_red']},  # Back to state 2
    ]
    
    test_responses = [
        '{"Agent[0.5, 0.5]": "move(workpiece_blue, position[0.0, 1.0])"}',
        '{"Agent[0.5, 0.5]": "move(workpiece_blue, position[0.0, 0.0])"}',
        '{"Agent[0.5, 0.5]": "move(workpiece_blue, position[0.0, 1.0])"}',  # Repeat
    ]
    
    print("\nTest 1: State-action cycle detection")
    is_deadlock, cycle_length = detect_deadlock(test_states, test_responses)
    print(f"Deadlock Detected: {is_deadlock}")
    print(f"Cycle Length: {cycle_length}")
    print(f"Result: {'PASS' if is_deadlock else 'FAIL'}")
    
    # Test progress stagnation
    print("\nTest 2: Progress stagnation detection")
    stagnant_states = [
        {'0.0_0.0': ['workpiece_blue', 'workpiece_red'], '1.0_0.0': []},
        {'0.0_0.0': ['workpiece_blue', 'workpiece_red'], '1.0_0.0': []},
        {'0.0_0.0': ['workpiece_blue', 'workpiece_red'], '1.0_0.0': []},
        {'0.0_0.0': ['workpiece_blue', 'workpiece_red'], '1.0_0.0': []},
        {'0.0_0.0': ['workpiece_blue', 'workpiece_red'], '1.0_0.0': []},
    ]
    
    is_deadlock, cycle_length = detect_deadlock(stagnant_states, [])
    print(f"Stagnation Deadlock Detected: {is_deadlock}")
    print(f"Result: {'PASS' if is_deadlock else 'FAIL'}")

def test_endpoint_load_balancing():
    """Test the endpoint load balancing and failover logic"""
    print("\n" + "=" * 60)
    print("TESTING: Endpoint Load Balancing")
    print("=" * 60)
    
    print("\nTest 1: Get available client")
    client, endpoint_name = get_available_client()
    print(f"Available Client: {client is not None}")
    print(f"Endpoint Name: {endpoint_name}")
    print(f"Result: {'PASS' if client is not None else 'FAIL'}")
    
    print("\nTest 2: Mark endpoint unavailable and failover")
    if endpoint_name:
        mark_endpoint_unavailable(endpoint_name, "Test error")
        new_client, new_endpoint = get_available_client()
        print(f"Failover Client: {new_client is not None}")
        print(f"New Endpoint: {new_endpoint}")
        print(f"Different Endpoint: {new_endpoint != endpoint_name}")
        
        # Restore endpoint
        mark_endpoint_available(endpoint_name)
        print(f"Result: {'PASS' if new_endpoint != endpoint_name else 'FAIL'}")

async def test_async_coordination():
    """Test the enhanced async coordination mechanisms"""
    print("\n" + "=" * 60)
    print("TESTING: Async Coordination Mechanisms")
    print("=" * 60)
    
    print("\nTest 1: Async endpoint availability")
    client, endpoint_name = get_available_async_client()
    print(f"Async Client Available: {client is not None}")
    print(f"Endpoint: {endpoint_name}")
    print(f"Result: {'PASS' if client is not None else 'FAIL'}")
    
    print("\nTest 2: Timeout mechanism simulation")
    start_time = time.time()
    
    # Simulate a coordination phase with timeout
    try:
        await asyncio.wait_for(asyncio.sleep(2), timeout=1)  # Should timeout
        print("Timeout test: FAIL (should have timed out)")
    except asyncio.TimeoutError:
        elapsed = time.time() - start_time
        print(f"Timeout test: PASS (timed out after {elapsed:.2f}s)")

def test_strategic_intervention():
    """Test the strategic intervention system"""
    print("\n" + "=" * 60)
    print("TESTING: Strategic Intervention System")
    print("=" * 60)
    
    # Create a test scenario
    current_state = {
        '0.0_0.0': ['workpiece_blue'],
        '1.0_1.0': ['workpiece_red'],
        '2.0_2.0': []
    }
    
    target_positions = {
        '2.0_2.0': ['target_blue', 'target_red']
    }
    
    print("\nTest 1: Strategic move generation")
    strategic_moves = generate_strategic_moves(current_state, target_positions)
    print(f"Strategic moves generated: {len(strategic_moves)}")
    
    if strategic_moves:
        for i, move in enumerate(strategic_moves[:3]):
            print(f"  Move {i+1}: {move['reason']}")
            print(f"    Workpiece: {move['workpiece']}")
            print(f"    From: {move['from_pos']} To: {move['to_pos']}")
            print(f"    Priority: {move['priority']:.2f}")
    
    print(f"Result: {'PASS' if strategic_moves else 'FAIL'}")
    
    print("\nTest 2: Progress score calculation")
    progress_score = calculate_progress_score(current_state, target_positions)
    print(f"Progress Score: {progress_score}")
    print(f"Result: {'PASS' if progress_score < 0 else 'FAIL'}")  # Should be negative (distance-based)

async def run_integration_test():
    """Run a small integration test"""
    print("\n" + "=" * 60)
    print("TESTING: Integration Test")
    print("=" * 60)
    
    # This would run a small test scenario
    print("Integration test would require full environment setup...")
    print("Skipping for now - manual testing recommended")
    print("Result: SKIP")

async def main():
    """Run all tests"""
    print("Multi-Agent System Improvements Test Suite")
    print("=" * 60)
    
    # Run synchronous tests
    test_collision_detection()
    test_deadlock_detection()
    test_endpoint_load_balancing()
    test_strategic_intervention()
    
    # Run asynchronous tests
    await test_async_coordination()
    await run_integration_test()
    
    print("\n" + "=" * 60)
    print("TEST SUITE COMPLETED")
    print("=" * 60)
    print("\nKey Improvements Implemented:")
    print("✓ Enhanced collision detection with prevention")
    print("✓ Improved action validation with detailed feedback")
    print("✓ Multi-strategy deadlock detection")
    print("✓ Timeout mechanisms for coordination phases")
    print("✓ Load balancing and failover for API endpoints")
    print("✓ Enhanced response monitoring and validation")
    print("✓ Robust consensus mechanisms replacing 'I Agree'")
    print("✓ Conflict resolution protocols")
    print("✓ Strategic intervention system")
    
    print("\nRecommendations for further testing:")
    print("1. Run actual multi-agent scenarios with the improved system")
    print("2. Monitor response times and endpoint failover in practice")
    print("3. Test with various deadlock scenarios")
    print("4. Validate consensus mechanisms with different agent configurations")

if __name__ == "__main__":
    asyncio.run(main())
